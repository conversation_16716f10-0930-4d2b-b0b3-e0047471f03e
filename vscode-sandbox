#!/bin/bash

# VS Code Sandbox - Enhanced Isolation Tool
# Complete VS Code isolation solution with global installation and self-update
# 
# Author: <PERSON><PERSON><PERSON>
# Repository: https://github.com/MamunHoque/VSCodeSandbox
# Version: 3.0.0

set -euo pipefail

# Script metadata
SCRIPT_VERSION="3.0.0"
SCRIPT_NAME="vscode-sandbox"
REPOSITORY_URL="https://github.com/MamunHoque/VSCodeSandbox"
REPOSITORY_RAW_URL="https://raw.githubusercontent.com/MamunHoque/VSCodeSandbox/main"
INSTALL_PATH="/usr/local/bin/$SCRIPT_NAME"

# Configuration
ISOLATION_ROOT="${VSCODE_ISOLATION_ROOT:-$HOME/.vscode-isolated}"
PROFILE_NAME="${1:-}"
COMMAND="${2:-create}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# Logging functions
log_info() { echo -e "${BLUE}ℹ${NC} $1"; }
log_success() { echo -e "${GREEN}✅${NC} $1"; }
log_warning() { echo -e "${YELLOW}⚠${NC} $1"; }
log_error() { echo -e "${RED}❌${NC} $1"; }
log_header() { echo -e "${PURPLE}🚀${NC} $1"; }
log_step() { echo -e "${CYAN}▶${NC} $1"; }

# Show banner
show_banner() {
    echo -e "${CYAN}"
    cat << 'EOF'
╔══════════════════════════════════════════════════════════════╗
║                    VS Code Sandbox v3.0                    ║
║              Complete VS Code Isolation Solution            ║
║                                                              ║
║  🔒 Complete Isolation  🏠 Fresh OS Simulation              ║
║  🚫 Zero Interference   🔄 Multiple Profiles                ║
║  🗂️ Advanced Management  🧪 Well Tested                     ║
║  🌐 Global Installation  🔄 Self-Updating                   ║
╚══════════════════════════════════════════════════════════════╝
EOF
    echo -e "${NC}"
}

# Show usage information
show_usage() {
    echo -e "${WHITE}USAGE:${NC}"
    echo -e "    $SCRIPT_NAME <profile-name> [command] [options]"
    echo -e "    $SCRIPT_NAME [global-command] [options]"
    echo
    echo -e "${WHITE}PROFILE COMMANDS:${NC}"
    echo -e "    ${CYAN}create${NC}          - Create and launch isolated VS Code profile (default)"
    echo -e "    ${CYAN}launch${NC}          - Launch existing profile"
    echo -e "    ${CYAN}remove${NC}          - Remove profile completely"
    echo -e "    ${CYAN}status${NC}          - Show profile status and information"
    echo -e "    ${CYAN}scaffold${NC}        - Create project template within profile"
    echo -e "    ${CYAN}uri-status${NC}      - Show vscode:// URI handler status for profile"
    echo
    echo -e "${WHITE}GLOBAL COMMANDS:${NC}"
    echo -e "    ${CYAN}list${NC}            - List all isolated profiles"
    echo -e "    ${CYAN}clean${NC}           - Remove all profiles and projects"
    echo -e "    ${CYAN}fix-namespaces${NC}  - Fix namespace permission issues"
    echo -e "    ${CYAN}uninstall${NC}       - Completely remove VS Code Sandbox from system"
    echo -e "    ${CYAN}--help, -h${NC}      - Show this help message"
    echo -e "    ${CYAN}--version, -v${NC}   - Show version information"
    echo -e "    ${CYAN}--update${NC}        - Update to the latest version"
    echo -e "    ${CYAN}--install${NC}       - Install globally to /usr/local/bin"
    echo -e "    ${CYAN}--uninstall${NC}     - Remove global installation (legacy)"
    echo
    echo -e "${WHITE}SCAFFOLD OPTIONS:${NC}"
    echo -e "    ${CYAN}--type <type>${NC}   - Project type (react, node, python, go, static)"
    echo -e "    ${CYAN}--git${NC}           - Initialize Git repository"
    echo -e "    ${CYAN}--vscode${NC}        - Add VS Code configuration"
    echo -e "    ${CYAN}--docker${NC}        - Add Docker configuration"
    echo
    echo -e "${WHITE}ISOLATION OPTIONS:${NC}"
    echo -e "    ${CYAN}--max-security${NC}  - Enable maximum security with Linux namespaces"
    echo -e "    ${CYAN}--basic${NC}         - Use basic isolation (default)"
    echo -e "    ${CYAN}--desktop${NC}       - Add desktop integration (with --max-security)"
    echo -e "    ${CYAN}--no-extensions${NC} - Skip automatic extension installation"
    echo -e "    ${CYAN}--force-namespaces${NC} - Force namespace isolation even with snap VS Code"
    echo
    echo -e "${WHITE}URI SUPPORT:${NC}"
    echo -e "    ${CYAN}vscode://file/path${NC}              - Open specific file"
    echo -e "    ${CYAN}vscode://folder/path${NC}            - Open specific folder"
    echo -e "    ${CYAN}vscode://extension/id${NC}           - Install/open extension"
    echo -e "    ${CYAN}vscode://augment.vscode-augment/...${NC} - Augment extension URIs (auth, etc.)"
    echo -e "    ${CYAN}vscode-<profile>://file/path${NC}    - Profile-specific file URI"
    echo -e "    ${CYAN}file:///path/to/file${NC}            - Local file URI"
    echo -e "    ${CYAN}--open-url=vscode://...${NC}         - Explicit URI parameter"
    echo
    echo -e "${WHITE}EXAMPLES:${NC}"
    echo -e "    # Create isolated VS Code profile (basic isolation)"
    echo -e "    $SCRIPT_NAME myproject create"
    echo
    echo -e "    # Create maximum security isolated profile"
    echo -e "    $SCRIPT_NAME myproject create --max-security"
    echo
    echo -e "    # Create with desktop integration and maximum security"
    echo -e "    $SCRIPT_NAME myproject create --max-security --desktop"
    echo
    echo -e "    # Launch existing profile"
    echo -e "    $SCRIPT_NAME myproject launch"
    echo
    echo -e "    # Create project within isolated profile"
    echo -e "    $SCRIPT_NAME myproject scaffold --type react --git --vscode"
    echo
    echo -e "    # List all profiles"
    echo -e "    $SCRIPT_NAME list"
    echo
    echo -e "    # Update the tool"
    echo -e "    $SCRIPT_NAME --update"
    echo
    echo -e "    # Remove all profiles and projects"
    echo -e "    $SCRIPT_NAME clean"
    echo
    echo -e "    # Completely uninstall VS Code Sandbox"
    echo -e "    $SCRIPT_NAME uninstall"
    echo
    echo -e "${WHITE}ENVIRONMENT VARIABLES:${NC}"
    echo -e "    ${CYAN}VSCODE_ISOLATION_ROOT${NC}  - Root directory for profiles (default: ~/.vscode-isolated)"
    echo -e "    ${CYAN}VSCODE_BINARY${NC}          - Path to VS Code binary (default: auto-detect)"
    echo
    echo -e "${WHITE}REPOSITORY:${NC} $REPOSITORY_URL"
}

# Show version information
show_version() {
    echo -e "${WHITE}VS Code Sandbox${NC} version ${GREEN}$SCRIPT_VERSION${NC}"
    echo -e "Repository: ${BLUE}$REPOSITORY_URL${NC}"
    echo -e "Installation: ${CYAN}$INSTALL_PATH${NC}"
    echo -e "Isolation Root: ${CYAN}$ISOLATION_ROOT${NC}"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Detect VS Code binary
detect_vscode_binary() {
    local candidates=(
        "/snap/bin/code"
        "/usr/bin/code"
        "/usr/local/bin/code"
        "/opt/visual-studio-code/bin/code"
        "$(which code 2>/dev/null || true)"
    )

    for candidate in "${candidates[@]}"; do
        if [[ -x "$candidate" ]]; then
            echo "$candidate"
            return 0
        fi
    done

    log_error "VS Code binary not found. Please install VS Code or set VSCODE_BINARY environment variable."
    exit 1
}

VSCODE_BINARY="${VSCODE_BINARY:-$(detect_vscode_binary)}"

# Self-update functionality
self_update() {
    log_header "Updating VS Code Sandbox"
    
    # Check if we have write permissions
    if [[ ! -w "$INSTALL_PATH" ]] && [[ -f "$INSTALL_PATH" ]]; then
        log_error "No write permission to $INSTALL_PATH"
        log_info "Try running with sudo: sudo $SCRIPT_NAME --update"
        exit 1
    fi
    
    # Create temporary file
    local temp_file=$(mktemp)
    
    # Download latest version
    log_step "Downloading latest version..."
    if command_exists "curl"; then
        if curl -sSL "$REPOSITORY_RAW_URL/vscode-sandbox" -o "$temp_file"; then
            log_success "Downloaded latest version"
        else
            log_error "Failed to download update"
            rm -f "$temp_file"
            exit 1
        fi
    elif command_exists "wget"; then
        if wget -q "$REPOSITORY_RAW_URL/vscode-sandbox" -O "$temp_file"; then
            log_success "Downloaded latest version"
        else
            log_error "Failed to download update"
            rm -f "$temp_file"
            exit 1
        fi
    else
        log_error "Neither curl nor wget available for downloading"
        rm -f "$temp_file"
        exit 1
    fi
    
    # Verify the downloaded file
    if [[ ! -s "$temp_file" ]]; then
        log_error "Downloaded file is empty"
        rm -f "$temp_file"
        exit 1
    fi
    
    # Check if it's a valid script
    if ! head -1 "$temp_file" | grep -q "#!/bin/bash"; then
        log_error "Downloaded file is not a valid bash script"
        rm -f "$temp_file"
        exit 1
    fi
    
    # Backup current version if it exists
    if [[ -f "$INSTALL_PATH" ]]; then
        log_step "Backing up current version..."
        cp "$INSTALL_PATH" "${INSTALL_PATH}.backup"
    fi
    
    # Install new version
    log_step "Installing new version..."
    if [[ -f "$INSTALL_PATH" ]]; then
        # Update existing installation
        cp "$temp_file" "$INSTALL_PATH"
        chmod +x "$INSTALL_PATH"
    else
        # Install to current location if not globally installed
        cp "$temp_file" "$0"
        chmod +x "$0"
    fi
    
    # Clean up
    rm -f "$temp_file"
    
    log_success "Update completed successfully!"
    log_info "Restart your terminal or run the command again to use the new version"
}

# Install globally to /usr/local/bin
install_globally() {
    log_header "Installing VS Code Sandbox Globally"
    
    # Check if running as root or with sudo
    if [[ $EUID -ne 0 ]]; then
        log_error "Global installation requires root privileges"
        log_info "Run with sudo: sudo $0 --install"
        exit 1
    fi
    
    # Create /usr/local/bin if it doesn't exist
    mkdir -p /usr/local/bin
    
    # Copy script to global location
    log_step "Installing to $INSTALL_PATH..."
    cp "$0" "$INSTALL_PATH"
    chmod +x "$INSTALL_PATH"
    
    # Verify installation
    if [[ -x "$INSTALL_PATH" ]]; then
        log_success "Successfully installed to $INSTALL_PATH"
        log_info "You can now run 'vscode-sandbox' from anywhere"
        
        # Show version to confirm
        "$INSTALL_PATH" --version
    else
        log_error "Installation failed"
        exit 1
    fi
}

# Uninstall global installation
uninstall_globally() {
    log_header "Uninstalling VS Code Sandbox"
    
    # Check if running as root or with sudo
    if [[ $EUID -ne 0 ]]; then
        log_error "Global uninstallation requires root privileges"
        log_info "Run with sudo: sudo $0 --uninstall"
        exit 1
    fi
    
    if [[ -f "$INSTALL_PATH" ]]; then
        log_step "Removing $INSTALL_PATH..."
        rm -f "$INSTALL_PATH"
        log_success "VS Code Sandbox uninstalled successfully"
    else
        log_warning "VS Code Sandbox is not globally installed"
    fi
}

# Check namespace support
check_namespace_support() {
    if ! command_exists "unshare"; then
        log_error "unshare command not available. Please install util-linux package."
        log_info "Falling back to basic isolation mode..."
        MAX_SECURITY=false
        return 1
    fi

    # Test if we can create user namespaces
    if ! unshare -U true 2>/dev/null; then
        log_warning "User namespaces not available. Cannot use maximum security mode."
        log_warning "This could be due to:"
        log_warning "  • Kernel configuration (unprivileged_userns_clone disabled)"
        log_warning "  • System security policy"
        log_warning "  • Container environment restrictions"
        log_info ""
        log_info "To enable user namespaces, try:"
        log_info "  echo 1 | sudo tee /proc/sys/kernel/unprivileged_userns_clone"
        log_info ""
        log_info "Falling back to basic isolation mode..."
        MAX_SECURITY=false
        return 1
    fi

    return 0
}

# Profile paths
get_profile_paths() {
    PROFILE_ROOT="$ISOLATION_ROOT/profiles/$PROFILE_NAME"
    PROFILE_CONFIG="$PROFILE_ROOT/config"
    PROFILE_EXTENSIONS="$PROFILE_ROOT/extensions"
    PROFILE_PROJECTS="$PROFILE_ROOT/projects"
    LAUNCHER_SCRIPT="$ISOLATION_ROOT/launchers/$PROFILE_NAME-launcher.sh"

    # Maximum security paths
    PROFILE_HOME="$PROFILE_ROOT/home"
    PROFILE_CONFIG_MAX="$PROFILE_HOME/.config"
    PROFILE_CACHE="$PROFILE_HOME/.cache"
    PROFILE_LOCAL="$PROFILE_HOME/.local"
    PROFILE_TMP="$PROFILE_ROOT/tmp"
    NAMESPACE_SCRIPT="$ISOLATION_ROOT/launchers/$PROFILE_NAME-namespace.sh"
    DESKTOP_ENTRY="$PROFILE_LOCAL/share/applications/code-$PROFILE_NAME.desktop"
}

# Create isolated directory structure
create_profile_structure() {
    log_step "Creating isolated directory structure for profile '$PROFILE_NAME'"

    if [[ "$MAX_SECURITY" == true ]]; then
        create_max_security_structure
    else
        create_basic_structure
    fi
}

# Create basic isolation structure
create_basic_structure() {
    # Create all necessary directories
    mkdir -p "$PROFILE_ROOT"/{config,extensions,projects}
    mkdir -p "$ISOLATION_ROOT/launchers"

    # Create welcome file
    cat > "$PROFILE_PROJECTS/README.md" << EOF
# Welcome to $PROFILE_NAME Profile!

This is your isolated VS Code environment. Everything here is completely separated from other profiles and your main VS Code installation.

## What's Isolated:
- ✅ Extensions (installed separately for this profile)
- ✅ Settings and preferences
- ✅ Workspace configurations
- ✅ Recently opened files
- ✅ Git credentials and SSH keys
- ✅ Terminal history and environment

## Pre-installed Extensions:
- 🤖 **Augment** - AI-powered development assistant
- 📝 **Essential Development Extensions** - EditorConfig, TypeScript, Tailwind CSS

## Getting Started:
1. Install extensions specific to this profile
2. Configure settings as needed
3. Create or open projects in this directory
4. Everything stays isolated!

## Profile Information:
- **Profile Name**: $PROFILE_NAME
- **Profile Root**: $PROFILE_ROOT
- **Projects Directory**: $PROFILE_PROJECTS
- **Created**: $(date)

## Useful Commands:
\`\`\`bash
# Launch this profile
vscode-sandbox $PROFILE_NAME launch

# Create a project template
vscode-sandbox $PROFILE_NAME scaffold --type react --git

# Remove this profile
vscode-sandbox $PROFILE_NAME remove

# List all profiles
vscode-sandbox list
\`\`\`

Happy coding! 🚀
EOF

    log_success "Directory structure created"
}

# Create maximum security isolation structure
create_max_security_structure() {
    log_step "Creating maximum security isolation structure"

    # Create all necessary directories for maximum isolation
    mkdir -p "$PROFILE_HOME"/{.config,.cache,.local/{share/{applications,mime},bin},.vscode}
    mkdir -p "$PROFILE_TMP"
    mkdir -p "$PROFILE_PROJECTS"
    mkdir -p "$ISOLATION_ROOT/launchers"

    # Create isolated XDG directories
    mkdir -p "$PROFILE_CONFIG_MAX"/{Code,fontconfig,gtk-3.0,dconf}
    mkdir -p "$PROFILE_CACHE"/{Code,fontconfig}
    mkdir -p "$PROFILE_LOCAL/share"/{Code,applications,mime,fonts,themes,icons}

    # Create minimal environment files
    cat > "$PROFILE_HOME/.profile" << 'EOF'
# Isolated VS Code Profile Environment
export XDG_CONFIG_HOME="$HOME/.config"
export XDG_CACHE_HOME="$HOME/.cache"
export XDG_DATA_HOME="$HOME/.local/share"
export XDG_STATE_HOME="$HOME/.local/state"
export XDG_RUNTIME_DIR="/tmp/runtime-$(id -u)"
mkdir -p "$XDG_RUNTIME_DIR"
chmod 700 "$XDG_RUNTIME_DIR"
EOF

    # Create isolated fontconfig
    cat > "$PROFILE_CONFIG_MAX/fontconfig/fonts.conf" << 'EOF'
<?xml version="1.0"?>
<!DOCTYPE fontconfig SYSTEM "fonts.dtd">
<fontconfig>
    <dir>/usr/share/fonts</dir>
    <dir>/usr/local/share/fonts</dir>
    <dir>~/.local/share/fonts</dir>
    <cachedir>~/.cache/fontconfig</cachedir>
</fontconfig>
EOF

    # Create welcome file in projects directory
    cat > "$PROFILE_PROJECTS/README.md" << EOF
# Welcome to $PROFILE_NAME Profile! (Maximum Security)

This is your **maximum security** isolated VS Code environment using Linux namespaces. Everything here is completely separated from other profiles and your main VS Code installation.

## Maximum Security Features:
- ✅ **Process Isolation** - Separate PID namespace (can't see host processes)
- ✅ **Environment Isolation** - Separate HOME and XDG directories
- ✅ **Mount Isolation** - Separate filesystem view with controlled access
- ✅ **IPC Isolation** - Separate inter-process communication
- ✅ **UTS Isolation** - Separate hostname and domain name
- ✅ **Temporary File Isolation** - Separate /tmp directory

## What's Isolated:
- ✅ Extensions (installed separately for this profile)
- ✅ Settings and preferences
- ✅ Workspace configurations
- ✅ Recently opened files
- ✅ Git credentials and SSH keys
- ✅ Terminal history and environment
- ✅ **System processes** (completely isolated process space)
- ✅ **File system** (controlled mount points and access)
- ✅ **Network access** (can be isolated if needed)

## Pre-installed Extensions:
- 🤖 **Augment** - AI-powered development assistant
- 📝 **Essential Development Extensions** - EditorConfig, TypeScript, Tailwind CSS

## Profile Information:
- **Profile Name**: $PROFILE_NAME
- **Security Level**: Maximum (Linux Namespaces)
- **Profile Root**: $PROFILE_ROOT
- **Isolated Home**: $PROFILE_HOME
- **Projects Directory**: $PROFILE_PROJECTS
- **Created**: $(date)

## Useful Commands:
\`\`\`bash
# Launch this maximum security profile
vscode-sandbox $PROFILE_NAME launch

# Create a project template with maximum security
vscode-sandbox $PROFILE_NAME scaffold --type react --git

# Remove this profile completely
vscode-sandbox $PROFILE_NAME remove

# List all profiles
vscode-sandbox list
\`\`\`

**Enterprise-Grade Security** - Perfect for confidential projects! 🛡️
EOF

    log_success "Maximum security directory structure created"
}

# Install Augment extension automatically
install_augment_extension() {
    local profile_type="$1"  # "basic" or "max-security"

    log_step "Installing Augment extension automatically..."

    # Determine the correct extensions directory and user data directory
    local extensions_dir
    local user_data_dir

    if [[ "$profile_type" == "max-security" ]]; then
        extensions_dir="$PROFILE_LOCAL/share/Code/extensions"
        user_data_dir="$PROFILE_CONFIG_MAX/Code"
    else
        extensions_dir="$PROFILE_EXTENSIONS"
        user_data_dir="$PROFILE_CONFIG"
    fi

    # Create extensions directory if it doesn't exist
    mkdir -p "$extensions_dir"
    mkdir -p "$user_data_dir"

    # Install Augment extension using VS Code CLI
    log_info "Installing Augment extension to isolated profile..."

    # Try different possible Augment extension IDs
    local augment_extensions=(
        "augmentcode.augment"
        "augment.augment"
        "augment-code.augment"
        "augment.vscode-augment"
    )

    local augment_installed=false
    for ext_id in "${augment_extensions[@]}"; do
        if "$VSCODE_BINARY" \
            --user-data-dir="$user_data_dir" \
            --extensions-dir="$extensions_dir" \
            --install-extension "$ext_id" \
            --force \
            --disable-gpu-sandbox \
            --no-sandbox \
            >/dev/null 2>&1; then
            log_success "Augment extension installed successfully ($ext_id)"
            augment_installed=true
            break
        fi
    done

    if [[ "$augment_installed" != true ]]; then
        log_warning "Augment extension not found in marketplace with standard IDs"
        log_info "Creating placeholder for manual Augment installation..."

        # Create a reminder file for manual installation
        cat > "$extensions_dir/INSTALL_AUGMENT.md" << 'EOF'
# Install Augment Extension

The Augment extension could not be installed automatically.

## Manual Installation Options:

1. **From VS Code Marketplace:**
   - Open VS Code in this isolated profile
   - Go to Extensions (Ctrl+Shift+X)
   - Search for "Augment"
   - Install the official Augment extension

2. **From VSIX file:**
   - If you have the Augment extension VSIX file
   - Use: Extensions > Install from VSIX...

3. **From Command Line:**
   ```bash
   code --user-data-dir="USER_DATA_DIR" --extensions-dir="EXTENSIONS_DIR" --install-extension path/to/augment.vsix
   ```

This isolated profile is ready for Augment once you install it manually!
EOF

        log_info "Manual installation guide created at: $extensions_dir/INSTALL_AUGMENT.md"
        log_info "You can install Augment manually from the Extensions marketplace"

        # Try to find and install from local VSIX files
        local vsix_locations=(
            "$HOME/Downloads/augment*.vsix"
            "$HOME/Desktop/augment*.vsix"
            "/tmp/augment*.vsix"
            "./augment*.vsix"
        )

        for location in "${vsix_locations[@]}"; do
            if ls $location >/dev/null 2>&1; then
                log_info "Found potential Augment VSIX file: $location"
                for vsix_file in $location; do
                    if [[ -f "$vsix_file" ]]; then
                        log_info "Attempting to install from VSIX: $vsix_file"
                        if "$VSCODE_BINARY" \
                            --user-data-dir="$user_data_dir" \
                            --extensions-dir="$extensions_dir" \
                            --install-extension "$vsix_file" \
                            --force \
                            --disable-gpu-sandbox \
                            --no-sandbox \
                            >/dev/null 2>&1; then
                            log_success "Augment extension installed from VSIX: $vsix_file"
                            augment_installed=true
                            break 2
                        fi
                    fi
                done
            fi
        done
    fi

    # Also try to install some commonly useful extensions for development
    local common_extensions=(
        "editorconfig.editorconfig"
        "ms-vscode.vscode-typescript-next"
        "bradlc.vscode-tailwindcss"
    )

    log_info "Installing common development extensions..."
    for extension in "${common_extensions[@]}"; do
        if "$VSCODE_BINARY" \
            --user-data-dir="$user_data_dir" \
            --extensions-dir="$extensions_dir" \
            --install-extension "$extension" \
            --force \
            --disable-gpu-sandbox \
            --no-sandbox \
            >/dev/null 2>&1; then
            log_info "✅ Installed: $extension"
        else
            log_info "⚠️ Skipped: $extension (not available or failed)"
        fi
    done

    log_success "Extension installation completed"
}

# Create VS Code settings for better Augment integration
create_augment_optimized_settings() {
    local profile_type="$1"
    local settings_file

    if [[ "$profile_type" == "max-security" ]]; then
        settings_file="$PROFILE_CONFIG_MAX/Code/User/settings.json"
    else
        settings_file="$PROFILE_CONFIG/User/settings.json"
    fi

    # Create User directory if it doesn't exist
    mkdir -p "$(dirname "$settings_file")"

    log_step "Creating Augment-optimized VS Code settings..."

    cat > "$settings_file" << 'EOF'
{
    "editor.tabSize": 2,
    "editor.insertSpaces": true,
    "editor.detectIndentation": false,
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
        "source.fixAll": true
    },
    "files.trimTrailingWhitespace": true,
    "files.insertFinalNewline": true,
    "files.trimFinalNewlines": true,
    "editor.minimap.enabled": true,
    "editor.wordWrap": "on",
    "editor.lineNumbers": "on",
    "editor.rulers": [80, 120],
    "workbench.colorTheme": "Default Dark+",
    "workbench.iconTheme": "vs-seti",
    "terminal.integrated.fontSize": 14,
    "editor.fontSize": 14,
    "editor.fontFamily": "'Fira Code', 'Cascadia Code', 'JetBrains Mono', 'Source Code Pro', monospace",
    "editor.fontLigatures": true,
    "git.enableSmartCommit": true,
    "git.confirmSync": false,
    "git.autofetch": true,
    "extensions.autoUpdate": true,
    "update.mode": "start",
    "telemetry.telemetryLevel": "off",
    "workbench.startupEditor": "welcomePage",
    "editor.suggestSelection": "first",
    "editor.acceptSuggestionOnCommitCharacter": false,
    "editor.acceptSuggestionOnEnter": "on",
    "editor.quickSuggestions": {
        "other": true,
        "comments": false,
        "strings": false
    },
    "editor.parameterHints.enabled": true,
    "editor.hover.enabled": true,
    "editor.lightbulb.enabled": true
}
EOF

    log_success "Augment-optimized settings created"
}

# Create project-specific URI handlers
create_project_uri_handlers() {
    local profile_type="$1"

    log_step "Creating project-specific URI handlers..."

    # Create URI handler for each project in the profile
    if [[ -d "$PROFILE_PROJECTS" ]]; then
        for project_dir in "$PROFILE_PROJECTS"/*; do
            if [[ -d "$project_dir" ]]; then
                local project_name=$(basename "$project_dir")
                create_project_uri_handler "$project_name" "$project_dir" "$profile_type"
            fi
        done
    fi

    # Create a general project URI handler script
    local general_handler="$PROFILE_ROOT/bin/project-uri-handler"
    mkdir -p "$PROFILE_ROOT/bin"

    cat > "$general_handler" << EOF
#!/bin/bash
# Project URI Handler for profile: $PROFILE_NAME
# Handles project-specific vscode:// URIs

set -euo pipefail

PROFILE_NAME="$PROFILE_NAME"
PROFILE_PROJECTS="$PROFILE_PROJECTS"
LAUNCHER_SCRIPT="$LAUNCHER_SCRIPT"

# Parse URI to extract project and file information
URI="\$1"
PROJECT=""
FILE_PATH=""

# Extract project name from URI patterns like:
# vscode://file/path/to/project/file.js
# vscode://folder/path/to/project
if [[ "\$URI" =~ vscode://file/(.*) ]]; then
    FILE_PATH="\${BASH_REMATCH[1]}"
    # Try to determine project from file path
    for project_dir in "\$PROFILE_PROJECTS"/*; do
        if [[ -d "\$project_dir" ]]; then
            project_name=\$(basename "\$project_dir")
            if [[ "\$FILE_PATH" == *"/\$project_name/"* ]]; then
                PROJECT="\$project_name"
                break
            fi
        fi
    done
elif [[ "\$URI" =~ vscode://folder/(.*) ]]; then
    FOLDER_PATH="\${BASH_REMATCH[1]}"
    # Check if folder is within a project
    for project_dir in "\$PROFILE_PROJECTS"/*; do
        if [[ -d "\$project_dir" ]]; then
            project_name=\$(basename "\$project_dir")
            if [[ "\$FOLDER_PATH" == *"/\$project_name"* ]]; then
                PROJECT="\$project_name"
                break
            fi
        fi
    done
fi

# Log the URI handling
echo "[\$(date)] Handling URI: \$URI for profile: \$PROFILE_NAME, project: \$PROJECT" >> "\$PROFILE_ROOT/logs/project-uri.log"

# Launch VS Code with the URI
exec "\$LAUNCHER_SCRIPT" --open-url "\$URI"
EOF

    chmod +x "$general_handler"

    # Create logs directory
    mkdir -p "$PROFILE_ROOT/logs"

    log_success "Project-specific URI handlers created"
}

# Create URI handler for a specific project
create_project_uri_handler() {
    local project_name="$1"
    local project_path="$2"
    local profile_type="$3"

    local project_handler="$PROFILE_ROOT/bin/$project_name-uri-handler"

    cat > "$project_handler" << EOF
#!/bin/bash
# URI Handler for project: $project_name in profile: $PROFILE_NAME

set -euo pipefail

PROJECT_NAME="$project_name"
PROJECT_PATH="$project_path"
PROFILE_NAME="$PROFILE_NAME"
LAUNCHER_SCRIPT="$LAUNCHER_SCRIPT"

URI="\$1"

# Log the URI handling
echo "[\$(date)] Project \$PROJECT_NAME handling URI: \$URI" >> "$PROFILE_ROOT/logs/project-uri.log"

# Handle different URI types for this specific project
case "\$URI" in
    vscode://file/*)
        # Open specific file in this project
        exec "\$LAUNCHER_SCRIPT" --open-url "\$URI"
        ;;
    vscode://folder/*)
        # Open folder in this project
        exec "\$LAUNCHER_SCRIPT" --open-url "\$URI"
        ;;
    vscode-$PROFILE_NAME://*)
        # Profile-specific URI
        STANDARD_URI="\${URI/vscode-$PROFILE_NAME:/vscode:}"
        exec "\$LAUNCHER_SCRIPT" --open-url "\$STANDARD_URI"
        ;;
    *)
        # Default: open the project folder
        exec "\$LAUNCHER_SCRIPT" "\$PROJECT_PATH"
        ;;
esac
EOF

    chmod +x "$project_handler"
}

# Create namespace isolation script for maximum security
create_namespace_script() {
    log_step "Creating namespace isolation script"

    cat > "$NAMESPACE_SCRIPT" << EOF
#!/bin/bash
# Namespace isolation script for VS Code profile: $PROFILE_NAME
# Uses unprivileged user namespaces for complete isolation

set -euo pipefail

PROFILE_ROOT="$PROFILE_ROOT"
PROFILE_HOME="$PROFILE_HOME"
PROFILE_TMP="$PROFILE_TMP"
VSCODE_BINARY="$VSCODE_BINARY"

# Setup environment variables for complete isolation
export HOME="\$PROFILE_HOME"
export XDG_CONFIG_HOME="\$PROFILE_HOME/.config"
export XDG_CACHE_HOME="\$PROFILE_HOME/.cache"
export XDG_DATA_HOME="\$PROFILE_HOME/.local/share"
export XDG_STATE_HOME="\$PROFILE_HOME/.local/state"
export XDG_RUNTIME_DIR="\$PROFILE_TMP/runtime"
export TMPDIR="\$PROFILE_TMP"
export TMP="\$PROFILE_TMP"
export TEMP="\$PROFILE_TMP"

# Create runtime directory
mkdir -p "\$XDG_RUNTIME_DIR"
chmod 700 "\$XDG_RUNTIME_DIR"

# Ensure all required directories exist
mkdir -p "\$PROFILE_HOME"/{.config,.cache,.local/share}
mkdir -p "\$XDG_CONFIG_HOME/Code"
mkdir -p "\$XDG_DATA_HOME/Code/extensions"

# Source profile environment
source "\$PROFILE_HOME/.profile" 2>/dev/null || true

# Handle snap VS Code special requirements
if [[ "\$VSCODE_BINARY" == *"/snap/"* ]]; then
    # For snap VS Code, we need to set SNAP_USER_DATA to our isolated directory
    export SNAP_USER_DATA="\$PROFILE_HOME"
    export SNAP_USER_COMMON="\$PROFILE_HOME/.local/share"
fi

# Launch VS Code with complete isolation
# The namespace isolation is handled by the launcher script using unshare
exec "\$VSCODE_BINARY" \\
    --user-data-dir="\$XDG_CONFIG_HOME/Code" \\
    --extensions-dir="\$XDG_DATA_HOME/Code/extensions" \\
    --disable-gpu-sandbox \\
    --no-sandbox \\
    "\$@"
EOF

    chmod +x "$NAMESPACE_SCRIPT"
    log_success "Namespace script created"
}

# Create launcher script
create_launcher_script() {
    if [[ "$MAX_SECURITY" == true ]]; then
        create_max_security_launcher
    else
        create_basic_launcher
    fi
}

# Create basic launcher script
create_basic_launcher() {
    log_step "Creating basic launcher script with comprehensive URI support"

    cat > "$LAUNCHER_SCRIPT" << EOF
#!/bin/bash
# VS Code Launcher for isolated profile: $PROFILE_NAME
# Supports comprehensive URI handling

set -euo pipefail

PROFILE_NAME="$PROFILE_NAME"
PROFILE_ROOT="$PROFILE_ROOT"
PROFILE_CONFIG="$PROFILE_CONFIG"
PROFILE_EXTENSIONS="$PROFILE_EXTENSIONS"
PROFILE_PROJECTS="$PROFILE_PROJECTS"
VSCODE_BINARY="$VSCODE_BINARY"

# Check if profile exists
if [[ ! -d "\$PROFILE_ROOT" ]]; then
    echo "❌ Profile '\$PROFILE_NAME' does not exist"
    echo "💡 Create it first with: vscode-sandbox \$PROFILE_NAME create"
    exit 1
fi

# Parse arguments and handle various URI formats
URI=""
OPEN_FOLDER=""
OPEN_FILE=""
EXTRA_ARGS=()

# Enhanced argument processing
for arg in "\$@"; do
    case "\$arg" in
        vscode://*)
            URI="\$arg"
            ;;
        vscode-file://*)
            URI="\$arg"
            ;;
        vscode-$PROFILE_NAME://*)
            # Convert profile-specific URI to standard vscode:// URI
            URI="\${arg/vscode-$PROFILE_NAME:/vscode:}"
            ;;
        --open-url=*)
            URI="\${arg#--open-url=}"
            ;;
        --folder-uri=*)
            OPEN_FOLDER="\${arg#--folder-uri=}"
            ;;
        --file-uri=*)
            OPEN_FILE="\${arg#--file-uri=}"
            ;;
        file://*)
            # Handle file:// URIs by converting to local path
            local file_path="\${arg#file://}"
            if [[ -e "\$file_path" ]]; then
                if [[ -d "\$file_path" ]]; then
                    OPEN_FOLDER="\$file_path"
                else
                    OPEN_FILE="\$file_path"
                fi
            fi
            ;;
        --goto=*)
            # Handle --goto=file:line:column format
            URI="vscode://file\${arg#--goto=}"
            ;;
        *)
            # Check if it's a file or directory path
            if [[ -e "\$arg" && "\$arg" != --* ]]; then
                if [[ -d "\$arg" ]]; then
                    OPEN_FOLDER="\$arg"
                else
                    OPEN_FILE="\$arg"
                fi
            else
                EXTRA_ARGS+=("\$arg")
            fi
            ;;
    esac
done

# Build final arguments
FINAL_ARGS=(
    --user-data-dir="\$PROFILE_CONFIG"
    --extensions-dir="\$PROFILE_EXTENSIONS"
    --disable-gpu-sandbox
    --no-sandbox
)

# Add URI if specified
if [[ -n "\$URI" ]]; then
    FINAL_ARGS+=(--open-url "\$URI")
fi

# Add folder if specified (default to projects directory if none specified)
if [[ -n "\$OPEN_FOLDER" ]]; then
    FINAL_ARGS+=("\$OPEN_FOLDER")
elif [[ \${#EXTRA_ARGS[@]} -eq 0 && -z "\$URI" && -z "\$OPEN_FILE" ]]; then
    # No specific target, open projects directory
    FINAL_ARGS+=("\$PROFILE_PROJECTS")
fi

# Add file if specified
if [[ -n "\$OPEN_FILE" ]]; then
    FINAL_ARGS+=(--goto "\$OPEN_FILE")
fi

# Add extra arguments
FINAL_ARGS+=("\${EXTRA_ARGS[@]}")

# Launch VS Code with isolated profile
exec "\$VSCODE_BINARY" "\${FINAL_ARGS[@]}"
EOF

    chmod +x "$LAUNCHER_SCRIPT"
    log_success "Basic launcher script created with comprehensive URI support"
}

# Create maximum security launcher script
create_max_security_launcher() {
    log_step "Creating maximum security launcher script with comprehensive URI support"

    cat > "$LAUNCHER_SCRIPT" << EOF
#!/bin/bash
# Enhanced VS Code Launcher for profile: $PROFILE_NAME
# Uses Linux namespaces for complete isolation with comprehensive URI support

set -euo pipefail

PROFILE_NAME="$PROFILE_NAME"
NAMESPACE_SCRIPT="$NAMESPACE_SCRIPT"
PROFILE_ROOT="$PROFILE_ROOT"
PROFILE_HOME="$PROFILE_HOME"
PROFILE_TMP="$PROFILE_TMP"
VSCODE_BINARY="$VSCODE_BINARY"
FORCE_NAMESPACES="$FORCE_NAMESPACES"

# Parse arguments and handle various URI formats
ARGS=("\$@")
URI=""
OPEN_FOLDER=""
OPEN_FILE=""

# Enhanced argument processing
for arg in "\$@"; do
    case "\$arg" in
        vscode://*)
            URI="\$arg"
            ;;
        vscode-file://*)
            URI="\$arg"
            ;;
        vscode-$PROFILE_NAME://*)
            # Convert profile-specific URI to standard vscode:// URI
            URI="\${arg/vscode-$PROFILE_NAME:/vscode:}"
            ;;
        --open-url=*)
            URI="\${arg#--open-url=}"
            ;;
        --folder-uri=*)
            OPEN_FOLDER="\${arg#--folder-uri=}"
            ;;
        --file-uri=*)
            OPEN_FILE="\${arg#--file-uri=}"
            ;;
        file://*)
            # Handle file:// URIs by converting to local path
            local file_path="\${arg#file://}"
            if [[ -e "\$file_path" ]]; then
                if [[ -d "\$file_path" ]]; then
                    OPEN_FOLDER="\$file_path"
                else
                    OPEN_FILE="\$file_path"
                fi
            fi
            ;;
        --goto=*)
            # Handle --goto=file:line:column format
            URI="vscode://file\${arg#--goto=}"
            ;;
        *)
            # Check if it's a file or directory path
            if [[ -e "\$arg" && "\$arg" != --* ]]; then
                if [[ -d "\$arg" ]]; then
                    OPEN_FOLDER="\$arg"
                else
                    OPEN_FILE="\$arg"
                fi
            fi
            ;;
    esac
done

# Function to launch with maximum isolation
launch_isolated() {
    local extra_args=()

    # Add URI if specified
    if [[ -n "\$URI" ]]; then
        extra_args+=("--open-url" "\$URI")
    fi

    # Add folder if specified
    if [[ -n "\$OPEN_FOLDER" ]]; then
        # Ensure folder path is absolute and within allowed areas
        local abs_folder="\$(realpath "\$OPEN_FOLDER" 2>/dev/null || echo "\$OPEN_FOLDER")"
        extra_args+=("\$abs_folder")
    fi

    # Add file if specified
    if [[ -n "\$OPEN_FILE" ]]; then
        # Ensure file path is absolute
        local abs_file="\$(realpath "\$OPEN_FILE" 2>/dev/null || echo "\$OPEN_FILE")"
        extra_args+=("--goto" "\$abs_file")
    fi

    # Add any remaining arguments that weren't parsed
    for arg in "\$@"; do
        case "\$arg" in
            vscode://*|vscode-file://*|vscode-$PROFILE_NAME://*|--open-url=*|--folder-uri=*|--file-uri=*|file://*|--goto=*)
                # Skip already processed arguments
                ;;
            *)
                if [[ ! -e "\$arg" || "\$arg" == --* ]]; then
                    extra_args+=("\$arg")
                fi
                ;;
        esac
    done

    # Launch with namespace isolation
    # Use unprivileged user namespaces for complete isolation
    exec unshare \\
        --user \\
        --pid \\
        --fork \\
        --mount-proc \\
        "\$NAMESPACE_SCRIPT" "\${extra_args[@]}"
}

# Check if profile exists
if [[ ! -d "\$PROFILE_ROOT" ]]; then
    echo "❌ Profile '\$PROFILE_NAME' does not exist"
    echo "💡 Create it first with: vscode-sandbox \$PROFILE_NAME create"
    exit 1
fi

# Check if VS Code is snap-based and handle accordingly
if [[ "\$VSCODE_BINARY" == *"/snap/"* ]] && [[ "\${FORCE_NAMESPACES:-}" != "true" ]]; then
    echo "⚠️  Snap VS Code detected - using basic isolation instead of namespaces"
    echo "ℹ️  Snap packages have restrictions with user namespaces"
    echo "ℹ️  Use --force-namespaces to override (may cause issues)"

    # Use basic isolation for snap VS Code
    export HOME="\$PROFILE_HOME"
    export XDG_CONFIG_HOME="\$PROFILE_HOME/.config"
    export XDG_CACHE_HOME="\$PROFILE_HOME/.cache"
    export XDG_DATA_HOME="\$PROFILE_HOME/.local/share"
    export XDG_STATE_HOME="\$PROFILE_HOME/.local/state"
    export XDG_RUNTIME_DIR="\$PROFILE_TMP/runtime"
    export TMPDIR="\$PROFILE_TMP"
    export TMP="\$PROFILE_TMP"
    export TEMP="\$PROFILE_TMP"

    # Create runtime directory
    mkdir -p "\$XDG_RUNTIME_DIR"
    chmod 700 "\$XDG_RUNTIME_DIR"

    # Ensure all required directories exist
    mkdir -p "\$PROFILE_HOME"/{.config,.cache,.local/share}
    mkdir -p "\$XDG_CONFIG_HOME/Code"
    mkdir -p "\$XDG_DATA_HOME/Code/extensions"

    # Source profile environment
    source "\$PROFILE_HOME/.profile" 2>/dev/null || true

    # Build arguments for basic isolation
    FINAL_ARGS=(
        --user-data-dir="\$XDG_CONFIG_HOME/Code"
        --extensions-dir="\$XDG_DATA_HOME/Code/extensions"
        --disable-gpu-sandbox
        --no-sandbox
    )

    # Add URI if specified
    if [[ -n "\$URI" ]]; then
        FINAL_ARGS+=(--open-url "\$URI")
    fi

    # Add folder if specified
    if [[ -n "\$OPEN_FOLDER" ]]; then
        local abs_folder="\$(realpath "\$OPEN_FOLDER" 2>/dev/null || echo "\$OPEN_FOLDER")"
        FINAL_ARGS+=("\$abs_folder")
    fi

    # Add file if specified
    if [[ -n "\$OPEN_FILE" ]]; then
        local abs_file="\$(realpath "\$OPEN_FILE" 2>/dev/null || echo "\$OPEN_FILE")"
        FINAL_ARGS+=(--goto "\$abs_file")
    fi

    # Add any remaining arguments
    for arg in "\$@"; do
        case "\$arg" in
            vscode://*|vscode-file://*|vscode-$PROFILE_NAME://*|--open-url=*|--folder-uri=*|--file-uri=*|file://*|--goto=*)
                # Skip already processed arguments
                ;;
            *)
                if [[ ! -e "\$arg" || "\$arg" == --* ]]; then
                    FINAL_ARGS+=("\$arg")
                fi
                ;;
        esac
    done

    # Launch with basic isolation
    exec "\$VSCODE_BINARY" "\${FINAL_ARGS[@]}"
else
    # Launch with namespace isolation for non-snap VS Code
    launch_isolated
fi
EOF

    chmod +x "$LAUNCHER_SCRIPT"
    log_success "Maximum security launcher script created with comprehensive URI support"
}

# Register URI handlers for the profile
register_uri_handlers() {
    log_step "Registering vscode:// URI handlers for profile '$PROFILE_NAME'"

    # Create a simple desktop entry for URI handling (even for basic profiles)
    local desktop_dir="$HOME/.local/share/applications"
    mkdir -p "$desktop_dir"
    local simple_desktop_entry="$desktop_dir/vscode-$PROFILE_NAME.desktop"

    cat > "$simple_desktop_entry" << EOF
[Desktop Entry]
Name=VS Code - $PROFILE_NAME
Comment=Isolated VS Code environment for $PROFILE_NAME
Exec=$LAUNCHER_SCRIPT %u
Icon=code
Terminal=false
Type=Application
Categories=Development;IDE;
MimeType=x-scheme-handler/vscode;x-scheme-handler/vscode-$PROFILE_NAME;x-scheme-handler/vscode-file;
StartupNotify=true
StartupWMClass=code
NoDisplay=true
EOF

    # Update desktop database first
    if command_exists "update-desktop-database"; then
        update-desktop-database "$desktop_dir" 2>/dev/null || true
    fi

    # Register this profile to handle vscode:// URIs
    if command_exists "xdg-mime"; then
        # Try to register the handlers
        if xdg-mime default "vscode-$PROFILE_NAME.desktop" "x-scheme-handler/vscode" 2>/dev/null; then
            log_success "Registered profile '$PROFILE_NAME' to handle vscode:// URIs"
        else
            log_warning "Could not register vscode:// handler automatically"
            log_info "You may need to run manually: xdg-mime default vscode-$PROFILE_NAME.desktop x-scheme-handler/vscode"
        fi

        # Register profile-specific handler
        xdg-mime default "vscode-$PROFILE_NAME.desktop" "x-scheme-handler/vscode-$PROFILE_NAME" 2>/dev/null || true
    else
        log_warning "xdg-mime not available - URI handlers not registered"
        log_info "Install xdg-utils package to enable automatic URI registration"
    fi
}

# Create desktop integration for maximum security
create_desktop_integration() {
    if [[ "$MAX_SECURITY" != true ]] || [[ "$ENABLE_DESKTOP" != true ]]; then
        return
    fi

    log_step "Creating desktop integration with comprehensive URI support"

    # Create desktop entry
    cat > "$DESKTOP_ENTRY" << EOF
[Desktop Entry]
Name=VS Code - $PROFILE_NAME (Maximum Security)
Comment=Maximum security isolated VS Code environment for $PROFILE_NAME
Exec=$LAUNCHER_SCRIPT %u
Icon=code
Terminal=false
Type=Application
Categories=Development;IDE;
MimeType=x-scheme-handler/vscode;x-scheme-handler/vscode-$PROFILE_NAME;x-scheme-handler/vscode-file;x-scheme-handler/vscode-insiders;
StartupNotify=true
StartupWMClass=code
EOF

    # Create comprehensive MIME types for VS Code URIs
    mkdir -p "$PROFILE_LOCAL/share/mime/packages"
    cat > "$PROFILE_LOCAL/share/mime/packages/vscode-$PROFILE_NAME.xml" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<mime-info xmlns="http://www.freedesktop.org/standards/shared-mime-info">
    <mime-type type="x-scheme-handler/vscode-$PROFILE_NAME">
        <comment>VS Code $PROFILE_NAME Profile URI</comment>
        <glob pattern="vscode-$PROFILE_NAME:*"/>
    </mime-type>
    <mime-type type="x-scheme-handler/vscode">
        <comment>VS Code URI for $PROFILE_NAME Profile</comment>
        <glob pattern="vscode:*"/>
    </mime-type>
    <mime-type type="x-scheme-handler/vscode-file">
        <comment>VS Code File URI for $PROFILE_NAME Profile</comment>
        <glob pattern="vscode-file:*"/>
    </mime-type>
</mime-info>
EOF

    # Create URI handler script for comprehensive vscode:// support
    local uri_handler="$PROFILE_LOCAL/bin/vscode-uri-handler"
    mkdir -p "$PROFILE_LOCAL/bin"
    cat > "$uri_handler" << EOF
#!/bin/bash
# VS Code URI Handler for profile: $PROFILE_NAME
# Handles vscode://, vscode-file://, and profile-specific URIs

set -euo pipefail

URI="\$1"
PROFILE_NAME="$PROFILE_NAME"
LAUNCHER_SCRIPT="$LAUNCHER_SCRIPT"

# Log URI handling for debugging
echo "[\$(date)] Handling URI: \$URI for profile: \$PROFILE_NAME" >> "$PROFILE_LOCAL/logs/uri-handler.log"

# Parse different URI types
case "\$URI" in
    vscode://*)
        # Standard VS Code URI (vscode://file/path, vscode://extension/id, etc.)
        exec "\$LAUNCHER_SCRIPT" --open-url "\$URI"
        ;;
    vscode-file://*)
        # VS Code file URI
        exec "\$LAUNCHER_SCRIPT" --open-url "\$URI"
        ;;
    vscode-$PROFILE_NAME://*)
        # Profile-specific URI
        # Convert to standard vscode:// URI
        STANDARD_URI="\${URI/vscode-$PROFILE_NAME:/vscode:}"
        exec "\$LAUNCHER_SCRIPT" --open-url "\$STANDARD_URI"
        ;;
    *)
        # Fallback: treat as file path or folder
        if [[ -e "\$URI" ]]; then
            exec "\$LAUNCHER_SCRIPT" "\$URI"
        else
            exec "\$LAUNCHER_SCRIPT" --open-url "\$URI"
        fi
        ;;
esac
EOF

    chmod +x "$uri_handler"

    # Create logs directory
    mkdir -p "$PROFILE_LOCAL/logs"

    # Update MIME database for this profile
    if command_exists "update-mime-database"; then
        update-mime-database "$PROFILE_LOCAL/share/mime" 2>/dev/null || true
    fi

    # Update desktop database
    if command_exists "update-desktop-database"; then
        update-desktop-database "$PROFILE_LOCAL/share/applications" 2>/dev/null || true
    fi

    # Note: URI registration is handled by register_uri_handlers() function

    log_success "Desktop integration created with comprehensive URI support"
}

# Create profile function
create_profile() {
    if [[ -d "$PROFILE_ROOT" ]]; then
        log_warning "Profile '$PROFILE_NAME' already exists"
        read -p "Do you want to recreate it? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "Launching existing profile instead"
            launch_profile
            return
        fi
        remove_profile_internal
    fi

    if [[ "$MAX_SECURITY" == true ]]; then
        log_header "Creating MAXIMUM SECURITY isolated VS Code profile: $PROFILE_NAME"
        if ! check_namespace_support; then
            log_header "Creating isolated VS Code profile: $PROFILE_NAME (Basic Mode)"
        fi
    else
        log_header "Creating isolated VS Code profile: $PROFILE_NAME"
    fi

    create_profile_structure

    if [[ "$MAX_SECURITY" == true ]]; then
        create_namespace_script
    fi

    create_launcher_script
    register_uri_handlers
    create_desktop_integration

    # Create optimized settings for Augment
    if [[ "$MAX_SECURITY" == true ]]; then
        create_augment_optimized_settings "max-security"
    else
        create_augment_optimized_settings "basic"
    fi

    # Install Augment extension and common development extensions
    if [[ "$SKIP_EXTENSIONS" != true ]]; then
        if [[ "$MAX_SECURITY" == true ]]; then
            install_augment_extension "max-security"
        else
            install_augment_extension "basic"
        fi
    else
        log_info "Skipping automatic extension installation (--no-extensions flag)"
        log_info "You can install Augment and other extensions manually later"
    fi

    # Create project-specific URI handlers
    if [[ "$MAX_SECURITY" == true ]]; then
        create_project_uri_handlers "max-security"
    else
        create_project_uri_handlers "basic"
    fi

    log_success "Profile '$PROFILE_NAME' created successfully!"
    log_info "Launching isolated VS Code with Augment extension..."

    # Launch the profile
    "$LAUNCHER_SCRIPT" >/dev/null 2>&1 &

    echo
    if [[ "$MAX_SECURITY" == true ]]; then
        log_success "🛡️ VS Code '$PROFILE_NAME' is running with MAXIMUM SECURITY isolation!"
        echo -e "${BLUE}🔒${NC} Security Level: Maximum (Linux Namespaces)"
        echo -e "${BLUE}🏠${NC} Isolated Home: $PROFILE_HOME"
        echo -e "${BLUE}📁${NC} Projects directory: $PROFILE_PROJECTS"
        echo -e "${BLUE}🔧${NC} Launcher script: $LAUNCHER_SCRIPT"
        if [[ "$ENABLE_DESKTOP" == true ]]; then
            echo -e "${BLUE}🖥️${NC} Desktop entry: VS Code - $PROFILE_NAME (Maximum Security)"
        fi
        echo
        echo -e "${GREEN}🛡️ Maximum Security Features:${NC}"
        echo "   • Process isolation (separate PID namespace)"
        echo "   • Environment isolation (separate HOME directory)"
        echo "   • Mount isolation (controlled filesystem access)"
        echo "   • IPC isolation (separate inter-process communication)"
        echo "   • UTS isolation (separate hostname/domain)"
        echo "   • Temporary file isolation (separate /tmp)"
        echo "   • Augment extension pre-installed for AI assistance"
    else
        log_success "🚀 VS Code '$PROFILE_NAME' is running in complete isolation!"
        echo -e "${BLUE}🔒${NC} Security Level: Basic (Directory Isolation)"
        echo -e "${BLUE}📁${NC} Projects directory: $PROFILE_PROJECTS"
        echo -e "${BLUE}🔧${NC} Launcher script: $LAUNCHER_SCRIPT"
        echo -e "${BLUE}🤖${NC} Augment extension pre-installed for AI assistance"
    fi

    echo
    echo -e "${GREEN}💡 Tips:${NC}"
    echo "   • Each profile is completely isolated from others"
    echo "   • Augment extension is pre-installed for AI-powered development"
    echo "   • Use 'vscode-sandbox $PROFILE_NAME launch' to start this profile again"
    echo "   • Use 'vscode-sandbox $PROFILE_NAME scaffold --type react' to create projects"
    echo "   • Use 'vscode-sandbox $PROFILE_NAME remove' to completely remove this profile"
    echo "   • Use 'vscode-sandbox list' to see all profiles"
}

# Launch existing profile
launch_profile() {
    if [[ ! -d "$PROFILE_ROOT" ]]; then
        log_error "Profile '$PROFILE_NAME' does not exist"
        log_info "Create it first with: vscode-sandbox $PROFILE_NAME create"
        exit 1
    fi

    log_info "Launching isolated VS Code profile: $PROFILE_NAME"

    # Pass FORCE_NAMESPACES environment variable to launcher
    if [[ "$FORCE_NAMESPACES" == true ]]; then
        export FORCE_NAMESPACES=true
    fi

    exec "$LAUNCHER_SCRIPT" "$@"
}

# Remove profile completely
remove_profile_internal() {
    if [[ -d "$PROFILE_ROOT" ]]; then
        log_step "Removing profile directory: $PROFILE_ROOT"
        rm -rf "$PROFILE_ROOT"
    fi

    if [[ -f "$LAUNCHER_SCRIPT" ]]; then
        log_step "Removing launcher script: $LAUNCHER_SCRIPT"
        rm -f "$LAUNCHER_SCRIPT"
    fi
}

remove_profile() {
    if [[ ! -d "$PROFILE_ROOT" ]]; then
        log_warning "Profile '$PROFILE_NAME' does not exist"
        return
    fi

    echo -e "${YELLOW}⚠${NC} This will completely remove the isolated VS Code profile '$PROFILE_NAME'"
    echo -e "${YELLOW}⚠${NC} All settings, extensions, and data will be permanently deleted"
    echo
    read -p "Are you sure you want to remove profile '$PROFILE_NAME'? (y/N): " -n 1 -r
    echo

    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "Removing profile '$PROFILE_NAME'..."
        remove_profile_internal
        log_success "Profile '$PROFILE_NAME' removed successfully"
    else
        log_info "Profile removal cancelled"
    fi
}

# List all profiles
list_profiles() {
    local profiles_dir="$ISOLATION_ROOT/profiles"

    if [[ ! -d "$profiles_dir" ]]; then
        log_info "No isolated VS Code profiles found"
        log_info "Create one with: vscode-sandbox <profile_name> create"
        return
    fi

    local profiles=($(find "$profiles_dir" -maxdepth 1 -type d -not -path "$profiles_dir" -printf "%f\n" 2>/dev/null | sort))

    if [[ ${#profiles[@]} -eq 0 ]]; then
        log_info "No isolated VS Code profiles found"
        log_info "Create one with: vscode-sandbox <profile_name> create"
        return
    fi

    echo -e "${BLUE}📋 Isolated VS Code Profiles:${NC}"
    echo

    for profile in "${profiles[@]}"; do
        local profile_path="$profiles_dir/$profile"
        local launcher_path="$ISOLATION_ROOT/launchers/$profile-launcher.sh"
        local size=$(du -sh "$profile_path" 2>/dev/null | cut -f1)
        local status="❌ Not configured"

        if [[ -f "$launcher_path" ]]; then
            status="✅ Ready to launch"
        fi

        echo -e "${GREEN}📁${NC} ${WHITE}$profile${NC}"
        echo -e "   ${BLUE}Size:${NC} $size"
        echo -e "   ${BLUE}Status:${NC} $status"
        echo -e "   ${BLUE}Path:${NC} $profile_path"
        echo -e "   ${CYAN}Launch:${NC} vscode-sandbox $profile launch"
        echo
    done

    echo -e "${CYAN}💡 Tips:${NC}"
    echo "   • Use 'vscode-sandbox <profile> launch' to open a profile"
    echo "   • Use 'vscode-sandbox <profile> scaffold --type react' to create projects"
    echo "   • Use 'vscode-sandbox <profile> remove' to delete a profile"
    echo "   • Use 'vscode-sandbox clean' to remove ALL profiles"
}

# Clean all profiles and projects
clean_all_profiles() {
    log_header "Cleaning All VS Code Sandbox Profiles"

    # Check if isolation directory exists
    if [[ ! -d "$ISOLATION_ROOT" ]]; then
        log_info "No VS Code Sandbox profiles found"
        log_info "Isolation directory does not exist: $ISOLATION_ROOT"
        return 0
    fi

    # Count existing profiles
    local profile_count=0
    if [[ -d "$ISOLATION_ROOT/profiles" ]]; then
        profile_count=$(find "$ISOLATION_ROOT/profiles" -maxdepth 1 -type d | wc -l)
        ((profile_count--)) # Subtract 1 for the profiles directory itself
    fi

    if [[ $profile_count -eq 0 ]]; then
        log_info "No profiles found to clean"
        return 0
    fi

    # Show what will be removed
    echo -e "${YELLOW}⚠️  WARNING: This will permanently remove:${NC}"
    echo -e "   • ${RED}$profile_count profile(s)${NC}"
    echo -e "   • ${RED}All projects and code${NC}"
    echo -e "   • ${RED}All extensions and settings${NC}"
    echo -e "   • ${RED}All launcher scripts${NC}"
    echo -e "   • ${RED}Entire isolation directory: $ISOLATION_ROOT${NC}"
    echo

    # List profiles that will be removed
    if [[ $profile_count -gt 0 ]]; then
        echo -e "${CYAN}Profiles to be removed:${NC}"
        for profile_dir in "$ISOLATION_ROOT/profiles"/*; do
            if [[ -d "$profile_dir" ]]; then
                local profile_name=$(basename "$profile_dir")
                local security_level="Basic"
                if [[ -f "$ISOLATION_ROOT/launchers/$profile_name-namespace.sh" ]]; then
                    security_level="Maximum Security"
                fi
                echo -e "   • ${RED}$profile_name${NC} ($security_level)"
            fi
        done
        echo
    fi

    # Confirmation prompt
    echo -e "${YELLOW}This action cannot be undone!${NC}"
    read -p "Are you sure you want to remove ALL profiles and projects? (type 'yes' to confirm): " -r
    echo

    if [[ "$REPLY" != "yes" ]]; then
        log_info "Operation cancelled"
        return 0
    fi

    # Remove everything
    log_step "Removing all VS Code Sandbox data..."

    # Remove the entire isolation directory
    if rm -rf "$ISOLATION_ROOT"; then
        log_success "All profiles and projects removed successfully"
        echo
        echo -e "${GREEN}✅ Cleanup completed:${NC}"
        echo -e "   • Removed $profile_count profile(s)"
        echo -e "   • Removed all projects and code"
        echo -e "   • Removed all extensions and settings"
        echo -e "   • Removed all launcher scripts"
        echo -e "   • Removed isolation directory: $ISOLATION_ROOT"
        echo
        echo -e "${CYAN}💡 You can create new profiles anytime with:${NC}"
        echo -e "   vscode-sandbox myproject create"
    else
        log_error "Failed to remove isolation directory"
        log_info "You may need to run with sudo or check permissions"
        exit 1
    fi
}

# Fix namespace profiles with broken mount-based implementation
fix_namespace_profiles() {
    log_header "Fixing Namespace Profiles"

    # Check if isolation directory exists
    if [[ ! -d "$ISOLATION_ROOT" ]]; then
        log_info "No VS Code Sandbox profiles found"
        return 0
    fi

    # Find profiles with namespace scripts
    local namespace_profiles=()
    if [[ -d "$ISOLATION_ROOT/launchers" ]]; then
        for script in "$ISOLATION_ROOT/launchers"/*-namespace.sh; do
            if [[ -f "$script" ]]; then
                local profile_name=$(basename "$script" | sed 's/-namespace\.sh$//')
                namespace_profiles+=("$profile_name")
            fi
        done
    fi

    if [[ ${#namespace_profiles[@]} -eq 0 ]]; then
        log_info "No maximum security profiles found to fix"
        return 0
    fi

    echo -e "${CYAN}Found ${#namespace_profiles[@]} maximum security profile(s) to fix:${NC}"
    for profile in "${namespace_profiles[@]}"; do
        echo -e "   • ${YELLOW}$profile${NC}"
    done
    echo

    echo -e "${BLUE}This will update the namespace scripts to use unprivileged user namespaces${NC}"
    echo -e "${BLUE}instead of the problematic mount-based approach.${NC}"
    echo

    read -p "Do you want to fix these profiles? (y/N): " -n 1 -r
    echo

    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Operation cancelled"
        return 0
    fi

    # Fix each profile
    for profile_name in "${namespace_profiles[@]}"; do
        log_step "Fixing profile: $profile_name"

        # Set up profile paths
        PROFILE_NAME="$profile_name"
        get_profile_paths

        # Recreate the namespace script with the fixed implementation
        if [[ -f "$NAMESPACE_SCRIPT" ]]; then
            create_namespace_script
            log_success "Fixed namespace script for $profile_name"
        fi

        # Recreate the launcher script with the fixed implementation
        if [[ -f "$LAUNCHER_SCRIPT" ]]; then
            MAX_SECURITY=true
            create_max_security_launcher
            log_success "Fixed launcher script for $profile_name"
        fi
    done

    echo
    log_success "All namespace profiles have been fixed!"
    echo
    echo -e "${GREEN}✅ Fixed profiles:${NC}"
    for profile in "${namespace_profiles[@]}"; do
        echo -e "   • ${GREEN}$profile${NC} - Now uses unprivileged user namespaces"
    done
    echo
    echo -e "${CYAN}💡 You can now launch these profiles without permission errors:${NC}"
    for profile in "${namespace_profiles[@]}"; do
        echo -e "   vscode-sandbox $profile launch"
    done
}

# Completely uninstall VS Code Sandbox from the system
uninstall_vscode_sandbox() {
    log_header "Uninstalling VS Code Sandbox"

    # Check if running as root/sudo for system-wide removal
    local need_sudo=false
    if [[ -f "/usr/local/bin/vscode-sandbox" ]] && [[ ! -w "/usr/local/bin" ]]; then
        need_sudo=true
    fi

    # Show what will be removed
    echo -e "${YELLOW}⚠️  WARNING: This will completely remove VS Code Sandbox from your system:${NC}"
    echo

    # Check what exists and show removal plan
    local items_to_remove=()
    local profile_count=0

    # Check for global installation
    if [[ -f "/usr/local/bin/vscode-sandbox" ]]; then
        items_to_remove+=("Global installation: /usr/local/bin/vscode-sandbox")
    fi

    # Check for profiles and data
    if [[ -d "$ISOLATION_ROOT" ]]; then
        if [[ -d "$ISOLATION_ROOT/profiles" ]]; then
            profile_count=$(find "$ISOLATION_ROOT/profiles" -maxdepth 1 -type d 2>/dev/null | wc -l)
            ((profile_count--)) # Subtract 1 for the profiles directory itself
        fi
        items_to_remove+=("All profiles and data: $ISOLATION_ROOT ($profile_count profiles)")
    fi

    # Check for desktop integration files
    local desktop_files=()
    if [[ -d "$HOME/.local/share/applications" ]]; then
        while IFS= read -r -d '' file; do
            if [[ -f "$file" ]] && grep -q "vscode-sandbox\|VS Code Sandbox" "$file" 2>/dev/null; then
                desktop_files+=("$(basename "$file")")
            fi
        done < <(find "$HOME/.local/share/applications" -name "*.desktop" -print0 2>/dev/null)
    fi

    if [[ ${#desktop_files[@]} -gt 0 ]]; then
        items_to_remove+=("Desktop integration files: ${desktop_files[*]}")
    fi

    # Check for MIME type associations
    local mime_files=()
    if [[ -d "$HOME/.local/share/mime/packages" ]]; then
        while IFS= read -r -d '' file; do
            if [[ -f "$file" ]] && grep -q "vscode-sandbox\|VS Code Sandbox" "$file" 2>/dev/null; then
                mime_files+=("$(basename "$file")")
            fi
        done < <(find "$HOME/.local/share/mime/packages" -name "*.xml" -print0 2>/dev/null)
    fi

    if [[ ${#mime_files[@]} -gt 0 ]]; then
        items_to_remove+=("MIME type associations: ${mime_files[*]}")
    fi

    # Show removal plan
    if [[ ${#items_to_remove[@]} -eq 0 ]]; then
        log_info "VS Code Sandbox is not installed or no components found to remove"
        return 0
    fi

    echo -e "${RED}Items to be removed:${NC}"
    for item in "${items_to_remove[@]}"; do
        echo -e "   • ${RED}$item${NC}"
    done
    echo

    # Show sudo requirement if needed
    if [[ "$need_sudo" == true ]]; then
        echo -e "${YELLOW}Note: Sudo privileges required for global installation removal${NC}"
        echo
    fi

    # Confirmation prompt
    echo -e "${YELLOW}This action cannot be undone!${NC}"
    read -p "Are you sure you want to completely uninstall VS Code Sandbox? (type 'UNINSTALL' to confirm): " -r
    echo

    if [[ "$REPLY" != "UNINSTALL" ]]; then
        log_info "Uninstallation cancelled"
        return 0
    fi

    # Perform uninstallation
    local removed_items=()
    local failed_items=()

    # Remove global installation
    if [[ -f "/usr/local/bin/vscode-sandbox" ]]; then
        log_step "Removing global installation..."
        if [[ "$need_sudo" == true ]]; then
            if sudo rm -f "/usr/local/bin/vscode-sandbox" 2>/dev/null; then
                removed_items+=("Global installation")
            else
                failed_items+=("Global installation (permission denied)")
            fi
        else
            if rm -f "/usr/local/bin/vscode-sandbox" 2>/dev/null; then
                removed_items+=("Global installation")
            else
                failed_items+=("Global installation")
            fi
        fi
    fi

    # Remove all profiles and data
    if [[ -d "$ISOLATION_ROOT" ]]; then
        log_step "Removing all profiles and data..."
        if rm -rf "$ISOLATION_ROOT" 2>/dev/null; then
            removed_items+=("All profiles and data ($profile_count profiles)")
        else
            failed_items+=("Profiles and data")
        fi
    fi

    # Remove desktop integration files
    if [[ ${#desktop_files[@]} -gt 0 ]]; then
        log_step "Removing desktop integration files..."
        local desktop_removed=()
        for file in "${desktop_files[@]}"; do
            if rm -f "$HOME/.local/share/applications/$file" 2>/dev/null; then
                desktop_removed+=("$file")
            fi
        done
        if [[ ${#desktop_removed[@]} -gt 0 ]]; then
            removed_items+=("Desktop files: ${desktop_removed[*]}")
        fi
    fi

    # Remove MIME type associations
    if [[ ${#mime_files[@]} -gt 0 ]]; then
        log_step "Removing MIME type associations..."
        local mime_removed=()
        for file in "${mime_files[@]}"; do
            if rm -f "$HOME/.local/share/mime/packages/$file" 2>/dev/null; then
                mime_removed+=("$file")
            fi
        done
        if [[ ${#mime_removed[@]} -gt 0 ]]; then
            removed_items+=("MIME files: ${mime_removed[*]}")
            # Update MIME database
            if command_exists "update-mime-database"; then
                update-mime-database "$HOME/.local/share/mime" 2>/dev/null || true
            fi
        fi
    fi

    # Update desktop database
    if command_exists "update-desktop-database"; then
        update-desktop-database "$HOME/.local/share/applications" 2>/dev/null || true
    fi

    # Show results
    echo
    if [[ ${#removed_items[@]} -gt 0 ]]; then
        log_success "VS Code Sandbox uninstallation completed"
        echo
        echo -e "${GREEN}✅ Successfully removed:${NC}"
        for item in "${removed_items[@]}"; do
            echo -e "   • ${GREEN}$item${NC}"
        done
    fi

    if [[ ${#failed_items[@]} -gt 0 ]]; then
        echo
        echo -e "${RED}❌ Failed to remove:${NC}"
        for item in "${failed_items[@]}"; do
            echo -e "   • ${RED}$item${NC}"
        done
        echo
        echo -e "${YELLOW}You may need to remove these manually with appropriate permissions${NC}"
    fi

    echo
    echo -e "${CYAN}💡 VS Code Sandbox has been removed from your system${NC}"
    echo -e "${CYAN}💡 You can reinstall anytime with:${NC}"
    echo -e "   curl -sSL https://raw.githubusercontent.com/MamunHoque/VSCodeSandbox/main/install-vscode-sandbox.sh | sudo bash"
}

# Show profile status
show_profile_status() {
    if [[ ! -d "$PROFILE_ROOT" ]]; then
        log_error "Profile '$PROFILE_NAME' does not exist"
        log_info "Create it first with: vscode-sandbox $PROFILE_NAME create"
        exit 1
    fi

    echo -e "${BLUE}📊 Profile Status: ${WHITE}$PROFILE_NAME${NC}"
    echo

    # Detect security level
    local security_level="Basic (Directory Isolation)"
    local security_icon="🔒"
    if [[ -f "$NAMESPACE_SCRIPT" ]]; then
        security_level="Maximum (Linux Namespaces)"
        security_icon="🛡️"
    fi

    # Basic information
    echo -e "${GREEN}📁 Basic Information:${NC}"
    echo -e "   ${BLUE}Profile Name:${NC} $PROFILE_NAME"
    echo -e "   ${BLUE}Security Level:${NC} $security_icon $security_level"
    echo -e "   ${BLUE}Profile Root:${NC} $PROFILE_ROOT"
    echo -e "   ${BLUE}Projects Directory:${NC} $PROFILE_PROJECTS"

    if [[ -f "$NAMESPACE_SCRIPT" ]]; then
        echo -e "   ${BLUE}Isolated Home:${NC} $PROFILE_HOME"
        echo -e "   ${BLUE}Config Directory:${NC} $PROFILE_CONFIG_MAX"
        echo -e "   ${BLUE}Extensions Directory:${NC} $PROFILE_LOCAL/share/Code/extensions"
    else
        echo -e "   ${BLUE}Config Directory:${NC} $PROFILE_CONFIG"
        echo -e "   ${BLUE}Extensions Directory:${NC} $PROFILE_EXTENSIONS"
    fi
    echo

    # Size information
    local total_size=$(du -sh "$PROFILE_ROOT" 2>/dev/null | cut -f1)
    local config_size=$(du -sh "$PROFILE_CONFIG" 2>/dev/null | cut -f1)
    local extensions_size=$(du -sh "$PROFILE_EXTENSIONS" 2>/dev/null | cut -f1)
    local projects_size=$(du -sh "$PROFILE_PROJECTS" 2>/dev/null | cut -f1)

    echo -e "${GREEN}💾 Storage Usage:${NC}"
    echo -e "   ${BLUE}Total Size:${NC} $total_size"
    echo -e "   ${BLUE}Config:${NC} $config_size"
    echo -e "   ${BLUE}Extensions:${NC} $extensions_size"
    echo -e "   ${BLUE}Projects:${NC} $projects_size"
    echo

    # Extensions count
    local ext_count=0
    if [[ -d "$PROFILE_EXTENSIONS" ]]; then
        ext_count=$(find "$PROFILE_EXTENSIONS" -maxdepth 1 -type d | wc -l)
        ((ext_count--)) # Subtract the parent directory
    fi

    # Projects count
    local project_count=0
    if [[ -d "$PROFILE_PROJECTS" ]]; then
        project_count=$(find "$PROFILE_PROJECTS" -maxdepth 1 -type d | wc -l)
        ((project_count--)) # Subtract the parent directory
    fi

    echo -e "${GREEN}📦 Content Summary:${NC}"
    echo -e "   ${BLUE}Installed Extensions:${NC} $ext_count"
    echo -e "   ${BLUE}Project Directories:${NC} $project_count"
    echo

    # Launcher status
    if [[ -f "$LAUNCHER_SCRIPT" ]]; then
        echo -e "${GREEN}🚀 Launcher:${NC} ✅ Available"
        echo -e "   ${BLUE}Script:${NC} $LAUNCHER_SCRIPT"
        echo -e "   ${CYAN}Command:${NC} vscode-sandbox $PROFILE_NAME launch"
    else
        echo -e "${GREEN}🚀 Launcher:${NC} ❌ Not configured"
    fi
    echo

    # Recent activity
    if [[ -f "$PROFILE_CONFIG/User/globalStorage/state.vscdb" ]]; then
        local last_modified=$(stat -c %y "$PROFILE_CONFIG/User/globalStorage/state.vscdb" 2>/dev/null | cut -d' ' -f1)
        echo -e "${GREEN}⏰ Last Activity:${NC} $last_modified"
    else
        echo -e "${GREEN}⏰ Last Activity:${NC} Never used"
    fi
}

# Show URI handler status for profile
show_uri_status() {
    if [[ ! -d "$PROFILE_ROOT" ]]; then
        log_error "Profile '$PROFILE_NAME' does not exist"
        log_info "Create it first with: vscode-sandbox $PROFILE_NAME create"
        exit 1
    fi

    echo -e "${BLUE}🔗 URI Handler Status: ${WHITE}$PROFILE_NAME${NC}"
    echo

    # Check if xdg-mime is available
    if ! command_exists "xdg-mime"; then
        log_warning "xdg-mime not available - cannot check URI handler status"
        return
    fi

    # Check current vscode:// handler
    local current_handler=$(xdg-mime query default x-scheme-handler/vscode 2>/dev/null || echo "none")
    local expected_handler="vscode-$PROFILE_NAME.desktop"

    # Check if it's registered in user's mimeapps.list
    local user_registered=false
    if [[ -f ~/.config/mimeapps.list ]] && grep -q "x-scheme-handler/vscode=$expected_handler" ~/.config/mimeapps.list 2>/dev/null; then
        user_registered=true
    fi

    echo -e "${GREEN}🎯 vscode:// URI Handler:${NC}"
    if [[ "$current_handler" == "$expected_handler" ]]; then
        echo -e "   ${GREEN}✅ Correctly configured${NC}"
        echo -e "   ${BLUE}Handler:${NC} $current_handler"
        echo -e "   ${BLUE}Profile:${NC} $PROFILE_NAME"
    elif [[ "$user_registered" == true ]]; then
        echo -e "   ${YELLOW}⚠️ Registered but overridden by system${NC}"
        echo -e "   ${BLUE}System Handler:${NC} $current_handler"
        echo -e "   ${BLUE}Your Handler:${NC} $expected_handler (registered in ~/.config/mimeapps.list)"
        if [[ "$current_handler" == *"snap"* ]] || [[ "$current_handler" == *"code_code"* ]]; then
            echo -e "   ${CYAN}Note:${NC} Snap VS Code may override user URI handlers"
            echo -e "   ${CYAN}Workaround:${NC} Use profile-specific URIs (vscode-$PROFILE_NAME://)"
        fi
    elif [[ "$current_handler" == "none" ]]; then
        echo -e "   ${YELLOW}⚠️ No handler configured${NC}"
        echo -e "   ${CYAN}Fix with:${NC} xdg-mime default $expected_handler x-scheme-handler/vscode"
    else
        echo -e "   ${RED}❌ Different handler configured${NC}"
        echo -e "   ${BLUE}Current:${NC} $current_handler"
        echo -e "   ${BLUE}Expected:${NC} $expected_handler"
        echo -e "   ${CYAN}Fix with:${NC} xdg-mime default $expected_handler x-scheme-handler/vscode"
        if [[ "$current_handler" == *"snap"* ]] || [[ "$current_handler" == *"code_code"* ]]; then
            echo -e "   ${CYAN}Note:${NC} Snap applications may have priority over user settings"
        fi
    fi
    echo

    # Check profile-specific handler
    local profile_handler=$(xdg-mime query default "x-scheme-handler/vscode-$PROFILE_NAME" 2>/dev/null || echo "none")
    local profile_registered=false
    if [[ -f ~/.config/mimeapps.list ]] && grep -q "x-scheme-handler/vscode-$PROFILE_NAME=$expected_handler" ~/.config/mimeapps.list 2>/dev/null; then
        profile_registered=true
    fi

    echo -e "${GREEN}🏷️ Profile-specific URI Handler (vscode-$PROFILE_NAME://)${NC}"
    if [[ "$profile_handler" == "$expected_handler" ]] || [[ "$profile_registered" == true ]]; then
        echo -e "   ${GREEN}✅ Correctly configured${NC}"
        echo -e "   ${BLUE}Handler:${NC} $expected_handler"
    else
        echo -e "   ${YELLOW}⚠️ Not configured${NC}"
        echo -e "   ${CYAN}Fix with:${NC} xdg-mime default $expected_handler x-scheme-handler/vscode-$PROFILE_NAME"
    fi
    echo

    # Check desktop entry
    local desktop_entry="$HOME/.local/share/applications/vscode-$PROFILE_NAME.desktop"
    echo -e "${GREEN}🖥️ Desktop Entry:${NC}"
    if [[ -f "$desktop_entry" ]]; then
        echo -e "   ${GREEN}✅ Available${NC}"
        echo -e "   ${BLUE}File:${NC} $desktop_entry"
    else
        echo -e "   ${YELLOW}⚠️ Not found${NC}"
        echo -e "   ${CYAN}Recreate with:${NC} vscode-sandbox $PROFILE_NAME create"
    fi
    echo

    # Test URI examples
    echo -e "${GREEN}🧪 Test URI Examples:${NC}"
    echo -e "   ${CYAN}File:${NC} vscode://file/path/to/file.js"
    echo -e "   ${CYAN}Folder:${NC} vscode://folder$PROFILE_PROJECTS"
    echo -e "   ${CYAN}Extension:${NC} vscode://extension/ms-python.python"
    echo -e "   ${CYAN}Augment Auth:${NC} vscode://augment.vscode-augment/auth/result?code=...&state=..."
    echo -e "   ${CYAN}Profile-specific:${NC} vscode-$PROFILE_NAME://file/path/to/file.js"
    echo

    # Quick fix command
    echo -e "${GREEN}🔧 Quick Fix Commands:${NC}"
    echo -e "   ${CYAN}Register this profile for vscode:// URIs:${NC}"
    echo -e "   xdg-mime default vscode-$PROFILE_NAME.desktop x-scheme-handler/vscode"
    echo
    echo -e "   ${CYAN}Register profile-specific URIs:${NC}"
    echo -e "   xdg-mime default vscode-$PROFILE_NAME.desktop x-scheme-handler/vscode-$PROFILE_NAME"
    echo
    echo -e "   ${CYAN}Test complex Augment URI:${NC}"
    echo -e "   vscode-sandbox $PROFILE_NAME launch --open-url 'vscode://augment.vscode-augment/auth/result?code=test'"
}

# Project scaffolding within isolated environment
scaffold_project() {
    if [[ ! -d "$PROFILE_ROOT" ]]; then
        log_error "Profile '$PROFILE_NAME' does not exist"
        log_info "Create it first with: vscode-sandbox $PROFILE_NAME create"
        exit 1
    fi

    # Parse scaffolding arguments
    local project_type=""
    local enable_git=false
    local enable_vscode=false
    local enable_docker=false
    local project_name=""

    # Parse remaining arguments
    shift 2 # Remove profile name and scaffold command
    while [[ $# -gt 0 ]]; do
        case $1 in
            --type)
                project_type="$2"
                shift 2
                ;;
            --git)
                enable_git=true
                shift
                ;;
            --vscode)
                enable_vscode=true
                shift
                ;;
            --docker)
                enable_docker=true
                shift
                ;;
            *)
                if [[ -z "$project_name" ]]; then
                    project_name="$1"
                else
                    log_error "Unknown argument: $1"
                    exit 1
                fi
                shift
                ;;
        esac
    done

    # Prompt for project name if not provided
    if [[ -z "$project_name" ]]; then
        read -p "Enter project name: " project_name
    fi

    # Prompt for project type if not provided
    if [[ -z "$project_type" ]]; then
        echo "Available project types:"
        echo "  1) react     - React application"
        echo "  2) node      - Node.js application"
        echo "  3) python    - Python project"
        echo "  4) go        - Go application"
        echo "  5) static    - Static HTML/CSS/JS"
        read -p "Select project type (1-5): " choice

        case $choice in
            1) project_type="react" ;;
            2) project_type="node" ;;
            3) project_type="python" ;;
            4) project_type="go" ;;
            5) project_type="static" ;;
            *) log_error "Invalid choice"; exit 1 ;;
        esac
    fi

    log_header "Creating $project_type project '$project_name' in isolated profile '$PROFILE_NAME'"

    # Create project in the isolated projects directory
    local project_path="$PROFILE_PROJECTS/$project_name"

    if [[ -d "$project_path" ]]; then
        log_error "Project '$project_name' already exists in profile '$PROFILE_NAME'"
        exit 1
    fi

    mkdir -p "$project_path"
    cd "$project_path"

    # Create project based on type
    case "$project_type" in
        "react")
            create_react_project_simple "$project_name"
            ;;
        "node")
            create_node_project_simple "$project_name"
            ;;
        "python")
            create_python_project_simple "$project_name"
            ;;
        "go")
            create_go_project_simple "$project_name"
            ;;
        "static")
            create_static_project_simple "$project_name"
            ;;
        *)
            log_error "Unsupported project type: $project_type"
            cd ..
            rmdir "$project_path" 2>/dev/null || true
            exit 1
            ;;
    esac

    # Add Git if requested
    if [[ "$enable_git" == true ]]; then
        log_step "Initializing Git repository..."
        git init
        create_gitignore_simple "$project_type"
        git add .
        git commit -m "Initial commit: $project_type project created in VS Code isolated profile"
        log_success "Git repository initialized"
    fi

    # Add VS Code config if requested
    if [[ "$enable_vscode" == true ]]; then
        log_step "Adding VS Code configuration..."
        mkdir -p .vscode
        create_vscode_settings_simple "$project_type"
        log_success "VS Code configuration added"
    fi

    # Add Docker if requested
    if [[ "$enable_docker" == true ]]; then
        log_step "Adding Docker configuration..."
        create_dockerfile_simple "$project_type"
        log_success "Docker configuration added"
    fi

    cd - >/dev/null

    # Create URI handler for this project
    create_project_uri_handler "$project_name" "$project_path" "basic"

    log_success "Project '$project_name' created successfully in isolated profile '$PROFILE_NAME'!"
    echo -e "${BLUE}📁${NC} Project path: $project_path"
    echo -e "${CYAN}💡${NC} Open with: vscode-sandbox $PROFILE_NAME launch"
    echo -e "${CYAN}🔗${NC} URI support: vscode://file$project_path/filename or vscode://folder$project_path"
}

# Simple project creation functions
create_react_project_simple() {
    local name="$1"
    cat > package.json << EOF
{
  "name": "$name",
  "version": "1.0.0",
  "description": "React project created in VS Code isolated profile",
  "main": "src/index.js",
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build",
    "test": "react-scripts test",
    "eject": "react-scripts eject"
  },
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-scripts": "5.0.1"
  },
  "browserslist": {
    "production": [">0.2%", "not dead", "not op_mini all"],
    "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]
  }
}
EOF

    mkdir -p src public
    cat > src/index.js << 'EOF'
import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(<App />);
EOF

    cat > src/App.js << EOF
import React from 'react';

function App() {
  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>Welcome to $name!</h1>
      <p>This React app was created in an isolated VS Code profile.</p>
      <p>🚀 Start building your amazing application!</p>
    </div>
  );
}

export default App;
EOF

    cat > public/index.html << EOF
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>$name</title>
</head>
<body>
    <div id="root"></div>
</body>
</html>
EOF
}

create_node_project_simple() {
    local name="$1"
    cat > package.json << EOF
{
  "name": "$name",
  "version": "1.0.0",
  "description": "Node.js project created in VS Code isolated profile",
  "main": "src/index.js",
  "scripts": {
    "start": "node src/index.js",
    "dev": "nodemon src/index.js"
  },
  "dependencies": {
    "express": "^4.18.2"
  },
  "devDependencies": {
    "nodemon": "^3.0.1"
  }
}
EOF

    mkdir -p src
    cat > src/index.js << EOF
const express = require('express');
const app = express();
const PORT = process.env.PORT || 3000;

app.use(express.json());

app.get('/', (req, res) => {
    res.json({
        message: 'Hello from $name!',
        profile: 'VS Code Isolated Profile',
        timestamp: new Date().toISOString()
    });
});

app.listen(PORT, () => {
    console.log(\`Server running on port \${PORT}\`);
});
EOF
}

create_python_project_simple() {
    local name="$1"
    cat > requirements.txt << 'EOF'
requests>=2.31.0
python-dotenv>=1.0.0
EOF

    mkdir -p src
    cat > src/main.py << EOF
#!/usr/bin/env python3
"""
$name - Created in VS Code isolated profile
"""

def main():
    print("Hello from $name!")
    print("This Python project was created in an isolated VS Code profile.")
    print("🐍 Start building your amazing application!")

if __name__ == "__main__":
    main()
EOF

    cat > README.md << EOF
# $name

Python project created in VS Code isolated profile.

## Setup

\`\`\`bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\\Scripts\\activate
pip install -r requirements.txt
\`\`\`

## Run

\`\`\`bash
python src/main.py
\`\`\`
EOF
}

create_go_project_simple() {
    local name="$1"
    go mod init "$name"

    cat > main.go << EOF
package main

import (
    "fmt"
    "net/http"
    "log"
)

func main() {
    http.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
        fmt.Fprintf(w, "Hello from $name!\\nCreated in VS Code isolated profile\\n🚀 Go application running!")
    })

    fmt.Println("Starting $name server on :8080")
    log.Fatal(http.ListenAndServe(":8080", nil))
}
EOF
}

create_static_project_simple() {
    local name="$1"
    cat > index.html << EOF
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>$name</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 40px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; }
        .highlight { background: #e3f2fd; padding: 20px; border-radius: 4px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Welcome to $name!</h1>
        <div class="highlight">
            <p>🎉 This static website was created in an isolated VS Code profile.</p>
            <p>✨ Start building your amazing website!</p>
        </div>
        <p>This project is completely isolated from other VS Code environments.</p>
    </div>
</body>
</html>
EOF
}

# Simple configuration file creators
create_gitignore_simple() {
    local project_type="$1"
    case "$project_type" in
        "react"|"node")
            cat > .gitignore << 'EOF'
node_modules/
.env
.env.local
dist/
build/
*.log
EOF
            ;;
        "python")
            cat > .gitignore << 'EOF'
__pycache__/
*.pyc
venv/
.env
*.log
EOF
            ;;
        "go")
            cat > .gitignore << 'EOF'
*.exe
*.out
.env
*.log
EOF
            ;;
        "static")
            cat > .gitignore << 'EOF'
.DS_Store
*.log
EOF
            ;;
    esac
}

create_vscode_settings_simple() {
    local project_type="$1"
    cat > .vscode/settings.json << 'EOF'
{
    "editor.tabSize": 2,
    "editor.insertSpaces": true,
    "editor.formatOnSave": true,
    "files.trimTrailingWhitespace": true,
    "files.insertFinalNewline": true
}
EOF
}

create_dockerfile_simple() {
    local project_type="$1"
    case "$project_type" in
        "react"|"node")
            cat > Dockerfile << 'EOF'
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
EOF
            ;;
        "python")
            cat > Dockerfile << 'EOF'
FROM python:3.11-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
CMD ["python", "src/main.py"]
EOF
            ;;
        "go")
            cat > Dockerfile << 'EOF'
FROM golang:1.21-alpine AS builder
WORKDIR /app
COPY . .
RUN go build -o main .

FROM alpine:latest
WORKDIR /root/
COPY --from=builder /app/main .
EXPOSE 8080
CMD ["./main"]
EOF
            ;;
    esac
}

# Parse command line arguments and execute
main() {
    # Initialize security options
    MAX_SECURITY=false
    ENABLE_DESKTOP=false
    SKIP_EXTENSIONS=false
    FORCE_NAMESPACES=false

    # Handle global commands first
    case "${1:-}" in
        "--help"|"-h"|"help")
            show_usage
            exit 0
            ;;
        "--version"|"-v"|"version")
            show_version
            exit 0
            ;;
        "--update"|"update")
            self_update
            exit 0
            ;;
        "--install"|"install")
            install_globally
            exit 0
            ;;
        "--uninstall")
            uninstall_globally
            exit 0
            ;;
        "list")
            list_profiles
            exit 0
            ;;
        "clean")
            clean_all_profiles
            exit 0
            ;;
        "fix-namespaces")
            fix_namespace_profiles
            exit 0
            ;;
        "uninstall")
            uninstall_vscode_sandbox
            exit 0
            ;;
        "")
            show_banner
            show_usage
            exit 0
            ;;
    esac

    # Handle profile-specific commands
    PROFILE_NAME="$1"
    COMMAND="${2:-create}"

    # Parse additional arguments for create command
    if [[ "$COMMAND" == "create" ]]; then
        shift 2 # Remove profile name and command
        while [[ $# -gt 0 ]]; do
            case $1 in
                --max-security)
                    MAX_SECURITY=true
                    shift
                    ;;
                --basic)
                    MAX_SECURITY=false
                    shift
                    ;;
                --desktop)
                    ENABLE_DESKTOP=true
                    shift
                    ;;
                --no-extensions)
                    SKIP_EXTENSIONS=true
                    shift
                    ;;
                --force-namespaces)
                    FORCE_NAMESPACES=true
                    shift
                    ;;
                *)
                    log_error "Unknown option for create command: $1"
                    show_usage
                    exit 1
                    ;;
            esac
        done
    fi

    # Validate profile name
    if [[ -z "$PROFILE_NAME" ]]; then
        log_error "Profile name is required"
        show_usage
        exit 1
    fi

    # Check for valid characters in profile name
    if [[ ! "$PROFILE_NAME" =~ ^[a-zA-Z0-9_-]+$ ]]; then
        log_error "Profile name can only contain letters, numbers, hyphens, and underscores"
        exit 1
    fi

    # Set up profile paths
    get_profile_paths

    # Execute command
    case "$COMMAND" in
        "create")
            create_profile
            ;;
        "launch")
            shift 2 # Remove profile name and command
            # Parse launch options
            while [[ $# -gt 0 ]]; do
                case $1 in
                    --force-namespaces)
                        FORCE_NAMESPACES=true
                        shift
                        ;;
                    *)
                        break
                        ;;
                esac
            done
            launch_profile "$@"
            ;;
        "remove")
            remove_profile
            ;;
        "status")
            show_profile_status
            ;;
        "uri-status")
            show_uri_status
            ;;
        "scaffold")
            scaffold_project "$@"
            ;;
        *)
            log_error "Unknown command: $COMMAND"
            echo
            show_usage
            exit 1
            ;;
    esac
}

# Run main function if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
